﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{6054F685-68B1-36F6-9E0A-C109769FD059}"
	ProjectSection(ProjectDependencies) = postProject
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0} = {7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}
		{F677CA74-E99D-3446-9B88-6E67E333229D} = {F677CA74-E99D-3446-9B88-6E67E333229D}
		{BFB12154-C890-38D9-8730-DA1972BD3E74} = {BFB12154-C890-38D9-8730-DA1972BD3E74}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "animalLib", "animal\animalLib.vcxproj", "{F677CA74-E99D-3446-9B88-6E67E333229D}"
	ProjectSection(ProjectDependencies) = postProject
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0} = {7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hello", "hello.vcxproj", "{BFB12154-C890-38D9-8730-DA1972BD3E74}"
	ProjectSection(ProjectDependencies) = postProject
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0} = {7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}
		{F677CA74-E99D-3446-9B88-6E67E333229D} = {F677CA74-E99D-3446-9B88-6E67E333229D}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6054F685-68B1-36F6-9E0A-C109769FD059}.Debug|x64.ActiveCfg = Debug|x64
		{6054F685-68B1-36F6-9E0A-C109769FD059}.Release|x64.ActiveCfg = Release|x64
		{6054F685-68B1-36F6-9E0A-C109769FD059}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6054F685-68B1-36F6-9E0A-C109769FD059}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.Debug|x64.ActiveCfg = Debug|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.Debug|x64.Build.0 = Debug|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.Release|x64.ActiveCfg = Release|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.Release|x64.Build.0 = Release|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7B043CB9-4AFB-3F33-ABFD-7A419D1F51D0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.Debug|x64.ActiveCfg = Debug|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.Debug|x64.Build.0 = Debug|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.Release|x64.ActiveCfg = Release|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.Release|x64.Build.0 = Release|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F677CA74-E99D-3446-9B88-6E67E333229D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.Debug|x64.ActiveCfg = Debug|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.Debug|x64.Build.0 = Debug|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.Release|x64.ActiveCfg = Release|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.Release|x64.Build.0 = Release|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BFB12154-C890-38D9-8730-DA1972BD3E74}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8E16B8BF-A50C-3944-AAE8-5438D2F3A8A5}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

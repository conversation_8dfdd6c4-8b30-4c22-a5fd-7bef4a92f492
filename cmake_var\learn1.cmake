cmake_minimum_required(VERSION 3.10)

# 打印信息(双引号为可选项)
message("hello")
message(cmake)
# 多行信息
message("abc
def")
message([[
xxx
yyy
]])

message("-------------------------")
# 内置宏定义
message(${CMAKE_VERSION})

# 变量
set(var1 "hi var1")
message(${var1})

# 环境变量
# message($ENV{PATH})

set(ENV{CXX} "g++")
message($ENV{CXX})

#unset{ENV{CXX}}
#message($ENV{CXX})

#list(命令字 对象 返回值)
list(APPEND list1 l11 l12 l13)
message(${list1})

list(LENGTH list1 list1_length)
message(${list1_length})

list(FIND list1 l13 index1)
message(${index1})

# list(REMOVE_ITEM list1 l11)
# message(${list1})

list(APPEND list1 l15)
list(INSERT list1 3 l14)
message(${list1})

list(REVERSE list1)
message(${list1})

list(SORT list1)
message(${list1})
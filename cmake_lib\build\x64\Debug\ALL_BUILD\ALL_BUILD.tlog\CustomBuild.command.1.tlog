^D:\CPP_WORKSPACE\LOVE-CMAKE\CMAKE_LIB\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/cpp_workspace/love-cmake/cmake_lib -BD:/cpp_workspace/love-cmake/cmake_lib/build --check-stamp-file D:/cpp_workspace/love-cmake/cmake_lib/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

cmake_minimum_required(VERSION 3.10)

project(hello CXX)

# 设置全局的库输出目录
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/a)      # 静态库和导入库 (.lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)    # 动态库 (Linux/Mac .so)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/bin)    # 可执行文件和Windows DLL

# 为不同配置类型设置输出目录（解决Debug/Release子目录问题）
foreach(OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES})
    string(TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG)
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${CMAKE_CURRENT_SOURCE_DIR}/a)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${CMAKE_CURRENT_SOURCE_DIR}/bin)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${CMAKE_CURRENT_SOURCE_DIR}/bin)
endforeach()

# 添加子目录，这会处理 animal 目录下的 CMakeLists.txt
add_subdirectory(animal)

# 创建可执行文件
add_executable(hello main.cpp)

# 链接库到可执行文件（推荐的现代 CMake 方式）
target_link_libraries(hello PRIVATE animalLib)

# 包含头文件目录（推荐的现代 CMake 方式）
target_include_directories(hello PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
# 演示：路径相对于当前目录(animal/)，不是根目录
message("当前目录: ${CMAKE_CURRENT_SOURCE_DIR}")
message("根目录: ${CMAKE_SOURCE_DIR}")

# 正确做法：使用完整路径，这样传递到父作用域时路径仍然正确
set(animal_source
    ${CMAKE_CURRENT_SOURCE_DIR}/dog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/cat.cpp
    PARENT_SCOPE  # 必须有这个！没有它，子目录中定义的变量无法在父目录中使用，这就是CMake的作用域规则。
)

# 如果写成这样就错了 - 会寻找 animal/animal/dog.cpp
# set(animal_source
#     animal/dog.cpp  # 错误！会寻找 animal/animal/dog.cpp
#     animal/cat.cpp  # 错误！会寻找 animal/animal/cat.cpp
#     PARENT_SCOPE
# )
# 查找当前目录下所有.cpp文件
file(GLOB src *.cpp)

# # 创建静态库
# add_library(animalLib STATIC ${src})

# # 设置库的包含目录（现代 CMake 方式）
# target_include_directories(animalLib PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# 创建动态库
add_library(animalLib SHARED ${src})
target_include_directories(animalLib PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# Windows动态库自动导出所有符号
set_target_properties(animalLib PROPERTIES
    WINDOWS_EXPORT_ALL_SYMBOLS TRUE                                   # 自动导出所有符号
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../a          # 导入库 (.lib)
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../bin        # Linux/Mac (.so)
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../bin        # Windows (.dll)
)
